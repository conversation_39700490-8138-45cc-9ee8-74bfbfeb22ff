# Photo Order System

A Next.js application that allows users to upload photos to Dropbox and create orders. Built with TypeScript, Tailwind CSS, and the Dropbox API.

## Features

- 📸 **Photo Upload**: Drag and drop or select multiple photos
- ☁️ **Dropbox Integration**: Automatic upload to Dropbox with organized folder structure
- 📋 **Order Management**: Create and track photo orders
- 🎨 **Modern UI**: Clean, responsive design with Tailwind CSS
- ⚡ **Real-time Progress**: Upload progress tracking
- 📱 **Mobile Friendly**: Responsive design for all devices

## Prerequisites

Before running this application, you need to set up a Dropbox app:

1. Go to [Dropbox Developers](https://www.dropbox.com/developers/apps)
2. Create a new app
3. Choose "Scoped access"
4. Choose "Full Dropbox" access
5. Give your app a name
6. Get your App Key, App Secret, and generate an Access Token

## Setup

1. **Clone and install dependencies:**
   ```bash
   cd photo-order-app
   npm install
   ```

2. **Environment Configuration:**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` and add your Dropbox credentials:
   ```env
   NEXT_PUBLIC_DROPBOX_APP_KEY=your_dropbox_app_key_here
   DROPBOX_APP_SECRET=your_dropbox_app_secret_here
   DROPBOX_ACCESS_TOKEN=your_dropbox_access_token_here
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

3. **Run the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## How It Works

1. **Customer Information**: Users enter their name and email
2. **Photo Upload**: Users can drag & drop or select multiple photos
3. **Dropbox Upload**: Photos are automatically uploaded to Dropbox in organized folders (`/orders/{orderId}/`)
4. **Order Creation**: System creates an order record with all uploaded photos
5. **Confirmation**: Users receive a confirmation with order details

## Project Structure

```
src/
├── app/
│   ├── api/
│   │   ├── orders/route.ts    # Order management API
│   │   └── upload/route.ts    # Photo upload API
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx               # Main application page
├── components/
│   ├── OrderForm.tsx          # Main order form component
│   ├── OrderSuccess.tsx       # Order confirmation component
│   └── PhotoUpload.tsx        # Photo upload component
├── lib/
│   └── dropbox.ts            # Dropbox integration utilities
└── types/
    └── index.ts              # TypeScript type definitions
```

## Technologies Used

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Dropbox API** - File storage
- **UUID** - Unique ID generation

## API Endpoints

- `POST /api/upload` - Upload individual photos to Dropbox
- `POST /api/orders` - Create new orders
- `GET /api/orders` - Retrieve orders (for future admin features)

## Development

To extend this application:

1. **Database Integration**: Replace in-memory order storage with a database (PostgreSQL, MongoDB, etc.)
2. **Authentication**: Add user authentication for order tracking
3. **Admin Panel**: Create an admin interface to manage orders
4. **Email Notifications**: Send confirmation emails to customers
5. **Payment Integration**: Add payment processing for orders

## Deployment

This app can be deployed on Vercel, Netlify, or any platform that supports Next.js:

1. **Vercel** (Recommended):
   ```bash
   npm run build
   vercel --prod
   ```

2. **Environment Variables**: Make sure to set all environment variables in your deployment platform

## License

MIT License - feel free to use this project as a starting point for your own applications.
