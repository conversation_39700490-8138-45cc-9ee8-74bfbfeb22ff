import { NextRequest, NextResponse } from 'next/server';
import { Order } from '@/types';

// In a real application, you would use a database
// For this demo, we'll use in-memory storage
const orders: Order[] = [];

export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();
    
    const newOrder: Order = {
      id: orderData.id,
      customerName: orderData.customerName,
      customerEmail: orderData.customerEmail,
      photos: orderData.photos,
      status: 'pending',
      createdAt: new Date(),
      totalPhotos: orderData.photos.length,
      notes: orderData.notes
    };

    orders.push(newOrder);

    return NextResponse.json({
      success: true,
      order: newOrder
    });
  } catch (error) {
    console.error('Order creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    orders: orders
  });
}
