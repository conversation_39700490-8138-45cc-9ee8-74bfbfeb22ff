import { NextRequest, NextResponse } from 'next/server';
import { uploadToDropbox, createOrderFolder } from '@/lib/dropbox';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const orderId = formData.get('orderId') as string;
    const fileName = formData.get('fileName') as string;

    if (!file || !orderId || !fileName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Ensure order folder exists
    await createOrderFolder(orderId);

    // Upload file to Dropbox
    const result = await uploadToDropbox(file, orderId, fileName);

    if (result.success) {
      return NextResponse.json({
        success: true,
        path: result.path
      });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
