'use client';

import React, { useState } from 'react';
import { Order } from '@/types';
import OrderForm from '@/components/OrderForm';
import OrderSuccess from '@/components/OrderSuccess';

export default function Home() {
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);

  const handleOrderCreated = (order: Order) => {
    setCurrentOrder(order);
  };

  const handleCreateAnother = () => {
    setCurrentOrder(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <h1 className="text-2xl font-bold text-gray-900">Photo Order System</h1>
          <p className="text-gray-600">Upload your photos to Dropbox and create orders</p>
        </div>
      </header>

      {/* Main Content */}
      <main className="py-8">
        {currentOrder ? (
          <OrderSuccess
            order={currentOrder}
            onCreateAnother={handleCreateAnother}
          />
        ) : (
          <OrderForm onOrderCreated={handleOrderCreated} />
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500">
            <p>&copy; 2024 Photo Order System. Built with Next.js and Dropbox.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
