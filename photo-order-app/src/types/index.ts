export interface PhotoFile {
  id: string;
  file: File;
  preview: string;
  uploaded: boolean;
  dropboxPath?: string;
  uploadProgress?: number;
}

export interface Order {
  id: string;
  customerName: string;
  customerEmail: string;
  photos: PhotoFile[];
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  createdAt: Date;
  totalPhotos: number;
  notes?: string;
}

export interface DropboxUploadResponse {
  success: boolean;
  path?: string;
  error?: string;
}
