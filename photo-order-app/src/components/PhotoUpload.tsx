'use client';

import React, { useState, useCallback } from 'react';
import { PhotoFile } from '@/types';
import { v4 as uuidv4 } from 'uuid';

interface PhotoUploadProps {
  onPhotosChange: (photos: PhotoFile[]) => void;
  orderId: string;
}

export default function PhotoUpload({ onPhotosChange, orderId }: PhotoUploadProps) {
  const [photos, setPhotos] = useState<PhotoFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [uploading, setUploading] = useState(false);

  const handleFileSelect = useCallback((files: FileList) => {
    const newPhotos: PhotoFile[] = Array.from(files)
      .filter(file => file.type.startsWith('image/'))
      .map(file => ({
        id: uuidv4(),
        file,
        preview: URL.createObjectURL(file),
        uploaded: false,
        uploadProgress: 0
      }));

    const updatedPhotos = [...photos, ...newPhotos];
    setPhotos(updatedPhotos);
    onPhotosChange(updatedPhotos);
  }, [photos, onPhotosChange]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const uploadPhoto = async (photo: PhotoFile) => {
    const formData = new FormData();
    formData.append('file', photo.file);
    formData.append('orderId', orderId);
    formData.append('fileName', photo.file.name);

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        return { ...photo, uploaded: true, dropboxPath: result.path, uploadProgress: 100 };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Upload failed:', error);
      return { ...photo, uploadProgress: 0 };
    }
  };

  const uploadAllPhotos = async () => {
    setUploading(true);
    
    const uploadPromises = photos
      .filter(photo => !photo.uploaded)
      .map(async (photo) => {
        // Update progress to show upload starting
        setPhotos(prev => prev.map(p => 
          p.id === photo.id ? { ...p, uploadProgress: 10 } : p
        ));
        
        const result = await uploadPhoto(photo);
        
        // Update the photo with result
        setPhotos(prev => prev.map(p => 
          p.id === photo.id ? result : p
        ));
        
        return result;
      });

    await Promise.all(uploadPromises);
    setUploading(false);
  };

  const removePhoto = (photoId: string) => {
    const updatedPhotos = photos.filter(photo => photo.id !== photoId);
    setPhotos(updatedPhotos);
    onPhotosChange(updatedPhotos);
  };

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragging 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="space-y-4">
          <div className="text-6xl">📸</div>
          <div>
            <p className="text-lg font-medium">Drop photos here or click to select</p>
            <p className="text-sm text-gray-500">Support for JPG, PNG, GIF files</p>
          </div>
          <input
            type="file"
            multiple
            accept="image/*"
            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
            className="hidden"
            id="photo-input"
          />
          <label
            htmlFor="photo-input"
            className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 cursor-pointer"
          >
            Select Photos
          </label>
        </div>
      </div>

      {/* Photo Preview Grid */}
      {photos.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Selected Photos ({photos.length})</h3>
            <button
              onClick={uploadAllPhotos}
              disabled={uploading || photos.every(p => p.uploaded)}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400"
            >
              {uploading ? 'Uploading...' : 'Upload All'}
            </button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {photos.map((photo) => (
              <div key={photo.id} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={photo.preview}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Upload Progress */}
                {photo.uploadProgress !== undefined && photo.uploadProgress > 0 && !photo.uploaded && (
                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1">
                    Uploading... {photo.uploadProgress}%
                  </div>
                )}
                
                {/* Upload Status */}
                {photo.uploaded && (
                  <div className="absolute top-2 right-2 bg-green-500 text-white rounded-full p-1">
                    ✓
                  </div>
                )}
                
                {/* Remove Button */}
                <button
                  onClick={() => removePhoto(photo.id)}
                  className="absolute top-2 left-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
                
                <p className="text-xs text-gray-500 mt-1 truncate">{photo.file.name}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
