'use client';

import React from 'react';
import { Order } from '@/types';

interface OrderSuccessProps {
  order: Order;
  onCreateAnother: () => void;
}

export default function OrderSuccess({ order, onCreateAnother }: OrderSuccessProps) {
  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="text-6xl mb-4">✅</div>
          <h1 className="text-3xl font-bold text-green-600 mb-2">Order Created Successfully!</h1>
          <p className="text-gray-600">Your photos have been uploaded and your order is being processed.</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-6 mb-6 text-left">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Order Details</h2>
          <div className="space-y-2">
            <p><span className="font-medium">Order ID:</span> {order.id}</p>
            <p><span className="font-medium">Customer:</span> {order.customerName}</p>
            <p><span className="font-medium">Email:</span> {order.customerEmail}</p>
            <p><span className="font-medium">Total Photos:</span> {order.totalPhotos}</p>
            <p><span className="font-medium">Status:</span> 
              <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
              </span>
            </p>
            <p><span className="font-medium">Created:</span> {order.createdAt.toLocaleString()}</p>
            {order.notes && (
              <p><span className="font-medium">Notes:</span> {order.notes}</p>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <p className="text-gray-600">
            We'll process your order and get back to you via email. 
            Your photos are safely stored in our system.
          </p>
          
          <div className="space-x-4">
            <button
              onClick={onCreateAnother}
              className="px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700"
            >
              Create Another Order
            </button>
            
            <button
              onClick={() => window.print()}
              className="px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700"
            >
              Print Receipt
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
