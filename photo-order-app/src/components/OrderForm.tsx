'use client';

import React, { useState } from 'react';
import { PhotoFile, Order } from '@/types';
import { v4 as uuidv4 } from 'uuid';
import PhotoUpload from './PhotoUpload';

interface OrderFormProps {
  onOrderCreated: (order: Order) => void;
}

export default function OrderForm({ onOrderCreated }: OrderFormProps) {
  const [orderId] = useState(() => uuidv4());
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [notes, setNotes] = useState('');
  const [photos, setPhotos] = useState<PhotoFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customerName || !customerEmail || photos.length === 0) {
      alert('Please fill in all required fields and add at least one photo.');
      return;
    }

    // Check if all photos are uploaded
    const unuploadedPhotos = photos.filter(photo => !photo.uploaded);
    if (unuploadedPhotos.length > 0) {
      alert('Please wait for all photos to finish uploading before submitting the order.');
      return;
    }

    setIsSubmitting(true);

    try {
      const orderData = {
        id: orderId,
        customerName,
        customerEmail,
        photos,
        notes
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      const result = await response.json();

      if (result.success) {
        onOrderCreated(result.order);
        // Reset form
        setCustomerName('');
        setCustomerEmail('');
        setNotes('');
        setPhotos([]);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Order submission failed:', error);
      alert('Failed to create order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Create Photo Order</h1>
        
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Customer Information */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">Customer Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="customerName"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your full name"
                />
              </div>
              
              <div>
                <label htmlFor="customerEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="customerEmail"
                  value={customerEmail}
                  onChange={(e) => setCustomerEmail(e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your email address"
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Special Instructions (Optional)
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Any special instructions for your order..."
              />
            </div>
          </div>

          {/* Photo Upload Section */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">Upload Photos</h2>
            <PhotoUpload onPhotosChange={setPhotos} orderId={orderId} />
          </div>

          {/* Order Summary */}
          {photos.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Order Summary</h3>
              <div className="space-y-2">
                <p><span className="font-medium">Order ID:</span> {orderId}</p>
                <p><span className="font-medium">Total Photos:</span> {photos.length}</p>
                <p><span className="font-medium">Uploaded:</span> {photos.filter(p => p.uploaded).length} / {photos.length}</p>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting || photos.length === 0 || photos.some(p => !p.uploaded)}
              className="px-8 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Creating Order...' : 'Create Order'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
