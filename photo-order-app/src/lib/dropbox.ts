import { Dropbox } from 'dropbox';

// Initialize Dropbox client
const dbx = new Dropbox({
  accessToken: process.env.DROPBOX_ACCESS_TOKEN,
  fetch: fetch
});

export interface UploadResult {
  success: boolean;
  path?: string;
  error?: string;
}

export async function uploadToDropbox(
  file: File,
  orderId: string,
  fileName: string
): Promise<UploadResult> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    
    const path = `/orders/${orderId}/${fileName}`;
    
    const response = await dbx.filesUpload({
      path: path,
      contents: uint8Array,
      mode: 'add',
      autorename: true
    });

    return {
      success: true,
      path: response.result.path_display || path
    };
  } catch (error) {
    console.error('Dropbox upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
}

export async function createOrderFolder(orderId: string): Promise<boolean> {
  try {
    await dbx.filesFolderCreate({
      path: `/orders/${orderId}`
    });
    return true;
  } catch (error) {
    // Folder might already exist, which is fine
    console.log('Folder creation note:', error);
    return true;
  }
}
