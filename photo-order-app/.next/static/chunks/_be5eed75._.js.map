{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/components/PhotoUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { PhotoFile } from '@/types';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface PhotoUploadProps {\n  onPhotosChange: (photos: PhotoFile[]) => void;\n  orderId: string;\n}\n\nexport default function PhotoUpload({ onPhotosChange, orderId }: PhotoUploadProps) {\n  const [photos, setPhotos] = useState<PhotoFile[]>([]);\n  const [isDragging, setIsDragging] = useState(false);\n  const [uploading, setUploading] = useState(false);\n\n  const handleFileSelect = useCallback((files: FileList) => {\n    const newPhotos: PhotoFile[] = Array.from(files)\n      .filter(file => file.type.startsWith('image/'))\n      .map(file => ({\n        id: uuidv4(),\n        file,\n        preview: URL.createObjectURL(file),\n        uploaded: false,\n        uploadProgress: 0\n      }));\n\n    const updatedPhotos = [...photos, ...newPhotos];\n    setPhotos(updatedPhotos);\n    onPhotosChange(updatedPhotos);\n  }, [photos, onPhotosChange]);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n    \n    if (e.dataTransfer.files) {\n      handleFileSelect(e.dataTransfer.files);\n    }\n  }, [handleFileSelect]);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  }, []);\n\n  const uploadPhoto = async (photo: PhotoFile) => {\n    const formData = new FormData();\n    formData.append('file', photo.file);\n    formData.append('orderId', orderId);\n    formData.append('fileName', photo.file.name);\n\n    try {\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        return { ...photo, uploaded: true, dropboxPath: result.path, uploadProgress: 100 };\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error('Upload failed:', error);\n      return { ...photo, uploadProgress: 0 };\n    }\n  };\n\n  const uploadAllPhotos = async () => {\n    setUploading(true);\n    \n    const uploadPromises = photos\n      .filter(photo => !photo.uploaded)\n      .map(async (photo) => {\n        // Update progress to show upload starting\n        setPhotos(prev => prev.map(p => \n          p.id === photo.id ? { ...p, uploadProgress: 10 } : p\n        ));\n        \n        const result = await uploadPhoto(photo);\n        \n        // Update the photo with result\n        setPhotos(prev => prev.map(p => \n          p.id === photo.id ? result : p\n        ));\n        \n        return result;\n      });\n\n    await Promise.all(uploadPromises);\n    setUploading(false);\n  };\n\n  const removePhoto = (photoId: string) => {\n    const updatedPhotos = photos.filter(photo => photo.id !== photoId);\n    setPhotos(updatedPhotos);\n    onPhotosChange(updatedPhotos);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Drop Zone */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n          isDragging \n            ? 'border-blue-500 bg-blue-50' \n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n      >\n        <div className=\"space-y-4\">\n          <div className=\"text-6xl\">📸</div>\n          <div>\n            <p className=\"text-lg font-medium\">Drop photos here or click to select</p>\n            <p className=\"text-sm text-gray-500\">Support for JPG, PNG, GIF files</p>\n          </div>\n          <input\n            type=\"file\"\n            multiple\n            accept=\"image/*\"\n            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}\n            className=\"hidden\"\n            id=\"photo-input\"\n          />\n          <label\n            htmlFor=\"photo-input\"\n            className=\"inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 cursor-pointer\"\n          >\n            Select Photos\n          </label>\n        </div>\n      </div>\n\n      {/* Photo Preview Grid */}\n      {photos.length > 0 && (\n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <h3 className=\"text-lg font-medium\">Selected Photos ({photos.length})</h3>\n            <button\n              onClick={uploadAllPhotos}\n              disabled={uploading || photos.every(p => p.uploaded)}\n              className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400\"\n            >\n              {uploading ? 'Uploading...' : 'Upload All'}\n            </button>\n          </div>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n            {photos.map((photo) => (\n              <div key={photo.id} className=\"relative group\">\n                <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n                  <img\n                    src={photo.preview}\n                    alt=\"Preview\"\n                    className=\"w-full h-full object-cover\"\n                  />\n                </div>\n                \n                {/* Upload Progress */}\n                {photo.uploadProgress !== undefined && photo.uploadProgress > 0 && !photo.uploaded && (\n                  <div className=\"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1\">\n                    Uploading... {photo.uploadProgress}%\n                  </div>\n                )}\n                \n                {/* Upload Status */}\n                {photo.uploaded && (\n                  <div className=\"absolute top-2 right-2 bg-green-500 text-white rounded-full p-1\">\n                    ✓\n                  </div>\n                )}\n                \n                {/* Remove Button */}\n                <button\n                  onClick={() => removePhoto(photo.id)}\n                  className=\"absolute top-2 left-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\"\n                >\n                  ×\n                </button>\n                \n                <p className=\"text-xs text-gray-500 mt-1 truncate\">{photo.file.name}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAWe,SAAS,YAAY,KAA6C;QAA7C,EAAE,cAAc,EAAE,OAAO,EAAoB,GAA7C;;IAClC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAc,EAAE;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAE3C,MAAM,mBAAmB,IAAA,4KAAW;qDAAC,CAAC;YACpC,MAAM,YAAyB,MAAM,IAAI,CAAC,OACvC,MAAM;uEAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;sEACpC,GAAG;uEAAC,CAAA,OAAQ,CAAC;wBACZ,IAAI,IAAA,4KAAM;wBACV;wBACA,SAAS,IAAI,eAAe,CAAC;wBAC7B,UAAU;wBACV,gBAAgB;oBAClB,CAAC;;YAEH,MAAM,gBAAgB;mBAAI;mBAAW;aAAU;YAC/C,UAAU;YACV,eAAe;QACjB;oDAAG;QAAC;QAAQ;KAAe;IAE3B,MAAM,aAAa,IAAA,4KAAW;+CAAC,CAAC;YAC9B,EAAE,cAAc;YAChB,cAAc;YAEd,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE;gBACxB,iBAAiB,EAAE,YAAY,CAAC,KAAK;YACvC;QACF;8CAAG;QAAC;KAAiB;IAErB,MAAM,iBAAiB,IAAA,4KAAW;mDAAC,CAAC;YAClC,EAAE,cAAc;YAChB,cAAc;QAChB;kDAAG,EAAE;IAEL,MAAM,kBAAkB,IAAA,4KAAW;oDAAC,CAAC;YACnC,EAAE,cAAc;YAChB,cAAc;QAChB;mDAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,MAAM,IAAI;QAClC,SAAS,MAAM,CAAC,WAAW;QAC3B,SAAS,MAAM,CAAC,YAAY,MAAM,IAAI,CAAC,IAAI;QAE3C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO;oBAAE,GAAG,KAAK;oBAAE,UAAU;oBAAM,aAAa,OAAO,IAAI;oBAAE,gBAAgB;gBAAI;YACnF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB;YAAE;QACvC;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QAEb,MAAM,iBAAiB,OACpB,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,QAAQ,EAC/B,GAAG,CAAC,OAAO;YACV,0CAA0C;YAC1C,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,gBAAgB;oBAAG,IAAI;YAGrD,MAAM,SAAS,MAAM,YAAY;YAEjC,+BAA+B;YAC/B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG,SAAS;YAG/B,OAAO;QACT;QAEF,MAAM,QAAQ,GAAG,CAAC;QAClB,aAAa;IACf;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC1D,UAAU;QACV,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAW,AAAC,uEAIX,OAHC,aACI,+BACA;gBAEN,QAAQ;gBACR,YAAY;gBACZ,aAAa;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAW;;;;;;sCAC1B,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BACC,MAAK;4BACL,QAAQ;4BACR,QAAO;4BACP,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAClE,WAAU;4BACV,IAAG;;;;;;sCAEL,6LAAC;4BACC,SAAQ;4BACR,WAAU;sCACX;;;;;;;;;;;;;;;;;YAOJ,OAAO,MAAM,GAAG,mBACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsB;oCAAkB,OAAO,MAAM;oCAAC;;;;;;;0CACpE,6LAAC;gCACC,SAAS;gCACT,UAAU,aAAa,OAAO,KAAK,CAAC,CAAA,IAAK,EAAE,QAAQ;gCACnD,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;kCAIlC,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAK,MAAM,OAAO;4CAClB,KAAI;4CACJ,WAAU;;;;;;;;;;;oCAKb,MAAM,cAAc,KAAK,aAAa,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,QAAQ,kBAChF,6LAAC;wCAAI,WAAU;;4CAAiF;4CAChF,MAAM,cAAc;4CAAC;;;;;;;oCAKtC,MAAM,QAAQ,kBACb,6LAAC;wCAAI,WAAU;kDAAkE;;;;;;kDAMnF,6LAAC;wCACC,SAAS,IAAM,YAAY,MAAM,EAAE;wCACnC,WAAU;kDACX;;;;;;kDAID,6LAAC;wCAAE,WAAU;kDAAuC,MAAM,IAAI,CAAC,IAAI;;;;;;;+BA/B3D,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;AAuChC;GA3LwB;KAAA", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/components/OrderForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { PhotoFile, Order } from '@/types';\nimport { v4 as uuidv4 } from 'uuid';\nimport PhotoUpload from './PhotoUpload';\n\ninterface OrderFormProps {\n  onOrderCreated: (order: Order) => void;\n}\n\nexport default function OrderForm({ onOrderCreated }: OrderFormProps) {\n  const [orderId] = useState(() => uuidv4());\n  const [customerName, setCustomerName] = useState('');\n  const [customerEmail, setCustomerEmail] = useState('');\n  const [notes, setNotes] = useState('');\n  const [photos, setPhotos] = useState<PhotoFile[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!customerName || !customerEmail || photos.length === 0) {\n      alert('Please fill in all required fields and add at least one photo.');\n      return;\n    }\n\n    // Check if all photos are uploaded\n    const unuploadedPhotos = photos.filter(photo => !photo.uploaded);\n    if (unuploadedPhotos.length > 0) {\n      alert('Please wait for all photos to finish uploading before submitting the order.');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const orderData = {\n        id: orderId,\n        customerName,\n        customerEmail,\n        photos,\n        notes\n      };\n\n      const response = await fetch('/api/orders', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(orderData)\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        onOrderCreated(result.order);\n        // Reset form\n        setCustomerName('');\n        setCustomerEmail('');\n        setNotes('');\n        setPhotos([]);\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error('Order submission failed:', error);\n      alert('Failed to create order. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">Create Photo Order</h1>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* Customer Information */}\n          <div className=\"space-y-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800\">Customer Information</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label htmlFor=\"customerName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Full Name *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"customerName\"\n                  value={customerName}\n                  onChange={(e) => setCustomerName(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Enter your full name\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"customerEmail\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address *\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"customerEmail\"\n                  value={customerEmail}\n                  onChange={(e) => setCustomerEmail(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Enter your email address\"\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Special Instructions (Optional)\n              </label>\n              <textarea\n                id=\"notes\"\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Any special instructions for your order...\"\n              />\n            </div>\n          </div>\n\n          {/* Photo Upload Section */}\n          <div className=\"space-y-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800\">Upload Photos</h2>\n            <PhotoUpload onPhotosChange={setPhotos} orderId={orderId} />\n          </div>\n\n          {/* Order Summary */}\n          {photos.length > 0 && (\n            <div className=\"bg-gray-50 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Order Summary</h3>\n              <div className=\"space-y-2\">\n                <p><span className=\"font-medium\">Order ID:</span> {orderId}</p>\n                <p><span className=\"font-medium\">Total Photos:</span> {photos.length}</p>\n                <p><span className=\"font-medium\">Uploaded:</span> {photos.filter(p => p.uploaded).length} / {photos.length}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              disabled={isSubmitting || photos.length === 0 || photos.some(p => !p.uploaded)}\n              className=\"px-8 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? 'Creating Order...' : 'Create Order'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;;;AALA;;;;AAWe,SAAS,UAAU,KAAkC;QAAlC,EAAE,cAAc,EAAkB,GAAlC;;IAChC,MAAM,CAAC,QAAQ,GAAG,IAAA,yKAAQ;8BAAC,IAAM,IAAA,4KAAM;;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAc,EAAE;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IAEjD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,OAAO,MAAM,KAAK,GAAG;YAC1D,MAAM;YACN;QACF;QAEA,mCAAmC;QACnC,MAAM,mBAAmB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,QAAQ;QAC/D,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,YAAY;gBAChB,IAAI;gBACJ;gBACA;gBACA;gBACA;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,eAAe,OAAO,KAAK;gBAC3B,aAAa;gBACb,gBAAgB;gBAChB,iBAAiB;gBACjB,SAAS;gBACT,UAAU,EAAE;YACd,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAA+C;;;;;;8DAGvF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAA+C;;;;;;8DAGxF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC,+IAAW;oCAAC,gBAAgB;oCAAW,SAAS;;;;;;;;;;;;wBAIlD,OAAO,MAAM,GAAG,mBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAE,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE;;;;;;;sDACnD,6LAAC;;8DAAE,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAoB;gDAAE,OAAO,MAAM;;;;;;;sDACpE,6LAAC;;8DAAE,6LAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;gDAAC;gDAAI,OAAO,MAAM;;;;;;;;;;;;;;;;;;;sCAMhH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU,gBAAgB,OAAO,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ;gCAC7E,WAAU;0CAET,eAAe,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;GAvJwB;KAAA", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/components/OrderSuccess.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Order } from '@/types';\n\ninterface OrderSuccessProps {\n  order: Order;\n  onCreateAnother: () => void;\n}\n\nexport default function OrderSuccess({ order, onCreateAnother }: OrderSuccessProps) {\n  return (\n    <div className=\"max-w-2xl mx-auto p-6\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8 text-center\">\n        <div className=\"mb-6\">\n          <div className=\"text-6xl mb-4\">✅</div>\n          <h1 className=\"text-3xl font-bold text-green-600 mb-2\">Order Created Successfully!</h1>\n          <p className=\"text-gray-600\">Your photos have been uploaded and your order is being processed.</p>\n        </div>\n\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6 text-left\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Order Details</h2>\n          <div className=\"space-y-2\">\n            <p><span className=\"font-medium\">Order ID:</span> {order.id}</p>\n            <p><span className=\"font-medium\">Customer:</span> {order.customerName}</p>\n            <p><span className=\"font-medium\">Email:</span> {order.customerEmail}</p>\n            <p><span className=\"font-medium\">Total Photos:</span> {order.totalPhotos}</p>\n            <p><span className=\"font-medium\">Status:</span> \n              <span className=\"ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm\">\n                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\n              </span>\n            </p>\n            <p><span className=\"font-medium\">Created:</span> {order.createdAt.toLocaleString()}</p>\n            {order.notes && (\n              <p><span className=\"font-medium\">Notes:</span> {order.notes}</p>\n            )}\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          <p className=\"text-gray-600\">\n            We'll process your order and get back to you via email. \n            Your photos are safely stored in our system.\n          </p>\n          \n          <div className=\"space-x-4\">\n            <button\n              onClick={onCreateAnother}\n              className=\"px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700\"\n            >\n              Create Another Order\n            </button>\n            \n            <button\n              onClick={() => window.print()}\n              className=\"px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700\"\n            >\n              Print Receipt\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,aAAa,KAA6C;QAA7C,EAAE,KAAK,EAAE,eAAe,EAAqB,GAA7C;IACnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,MAAM,EAAE;;;;;;;8CAC3D,6LAAC;;sDAAE,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,MAAM,YAAY;;;;;;;8CACrE,6LAAC;;sDAAE,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAa;wCAAE,MAAM,aAAa;;;;;;;8CACnE,6LAAC;;sDAAE,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAoB;wCAAE,MAAM,WAAW;;;;;;;8CACxE,6LAAC;;sDAAE,6LAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC/B,6LAAC;4CAAK,WAAU;sDACb,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;8CAG/D,6LAAC;;sDAAE,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAe;wCAAE,MAAM,SAAS,CAAC,cAAc;;;;;;;gCAC/E,MAAM,KAAK,kBACV,6LAAC;;sDAAE,6LAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAa;wCAAE,MAAM,KAAK;;;;;;;;;;;;;;;;;;;8BAKjE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAID,6LAAC;oCACC,SAAS,IAAM,OAAO,KAAK;oCAC3B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KAtDwB", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Order } from '@/types';\nimport OrderForm from '@/components/OrderForm';\nimport OrderSuccess from '@/components/OrderSuccess';\n\nexport default function Home() {\n  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);\n\n  const handleOrderCreated = (order: Order) => {\n    setCurrentOrder(order);\n  };\n\n  const handleCreateAnother = () => {\n    setCurrentOrder(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Photo Order System</h1>\n          <p className=\"text-gray-600\">Upload your photos to Dropbox and create orders</p>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"py-8\">\n        {currentOrder ? (\n          <OrderSuccess\n            order={currentOrder}\n            onCreateAnother={handleCreateAnother}\n          />\n        ) : (\n          <OrderForm onOrderCreated={handleOrderCreated} />\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-500\">\n            <p>&copy; 2024 Photo Order System. Built with Next.js and Dropbox.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;;;AALA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAe;IAE/D,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAK,WAAU;0BACb,6BACC,6LAAC,gJAAY;oBACX,OAAO;oBACP,iBAAiB;;;;;yCAGnB,6LAAC,6IAAS;oBAAC,gBAAgB;;;;;;;;;;;0BAK/B,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA3CwB;KAAA", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS;QAClE,IAAI,UAAU,MAAM,GAAG;QACvB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,UAAU,UAAU,IAAI,IACzC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,UACA,YACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACjE,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist/native.js"], "sourcesContent": ["const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,aAAa,OAAO,WAAW,eAAe,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC;uCACjF;IAAE;AAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist/rng.js"], "sourcesContent": ["let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n"], "names": [], "mappings": ";;;;AAAA,IAAI;AACJ,MAAM,QAAQ,IAAI,WAAW;AACd,SAAS;IACpB,IAAI,CAAC,iBAAiB;QAClB,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,eAAe,EAAE;YAC1D,MAAM,IAAI,MAAM;QACpB;QACA,kBAAkB,OAAO,eAAe,CAAC,IAAI,CAAC;IAClD;IACA,OAAO,gBAAgB;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,mJAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG;QAAE,SAAA,iEAAS;IAC1C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG;QAAE,SAAA,iEAAS;IAC7B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,IAAA,sJAAQ,EAAC,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction _v4(options, buf, offset) {\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    return _v4(options, buf, offset);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,SAAS,IAAI,OAAO,EAAE,GAAG,EAAE,MAAM;QAEE;IAD/B,UAAU,WAAW,CAAC;QACT,iBAAA;IAAb,MAAM,OAAO,CAAA,OAAA,CAAA,kBAAA,QAAQ,MAAM,cAAd,6BAAA,mBAAkB,eAAA,QAAQ,GAAG,cAAX,mCAAA,kBAAA,sBAAlB,kBAAA,OAAqC,IAAA,iJAAG;IACrD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,AAAC,mBAA4B,OAAV,QAAO,KAAe,OAAZ,SAAS,IAAG;QAClE;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,IAAA,+JAAe,EAAC;AAC3B;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC5B,IAAI,oJAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,oJAAM,CAAC,UAAU;IAC5B;IACA,OAAO,IAAI,SAAS,KAAK;AAC7B;uCACe", "ignoreList": [0], "debugId": null}}]}