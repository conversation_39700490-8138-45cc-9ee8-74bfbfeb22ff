{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/components/PhotoUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { PhotoFile } from '@/types';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface PhotoUploadProps {\n  onPhotosChange: (photos: PhotoFile[]) => void;\n  orderId: string;\n}\n\nexport default function PhotoUpload({ onPhotosChange, orderId }: PhotoUploadProps) {\n  const [photos, setPhotos] = useState<PhotoFile[]>([]);\n  const [isDragging, setIsDragging] = useState(false);\n  const [uploading, setUploading] = useState(false);\n\n  const handleFileSelect = useCallback((files: FileList) => {\n    const newPhotos: PhotoFile[] = Array.from(files)\n      .filter(file => file.type.startsWith('image/'))\n      .map(file => ({\n        id: uuidv4(),\n        file,\n        preview: URL.createObjectURL(file),\n        uploaded: false,\n        uploadProgress: 0\n      }));\n\n    const updatedPhotos = [...photos, ...newPhotos];\n    setPhotos(updatedPhotos);\n    onPhotosChange(updatedPhotos);\n  }, [photos, onPhotosChange]);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n    \n    if (e.dataTransfer.files) {\n      handleFileSelect(e.dataTransfer.files);\n    }\n  }, [handleFileSelect]);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  }, []);\n\n  const uploadPhoto = async (photo: PhotoFile) => {\n    const formData = new FormData();\n    formData.append('file', photo.file);\n    formData.append('orderId', orderId);\n    formData.append('fileName', photo.file.name);\n\n    try {\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        return { ...photo, uploaded: true, dropboxPath: result.path, uploadProgress: 100 };\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error('Upload failed:', error);\n      return { ...photo, uploadProgress: 0 };\n    }\n  };\n\n  const uploadAllPhotos = async () => {\n    setUploading(true);\n    \n    const uploadPromises = photos\n      .filter(photo => !photo.uploaded)\n      .map(async (photo) => {\n        // Update progress to show upload starting\n        setPhotos(prev => prev.map(p => \n          p.id === photo.id ? { ...p, uploadProgress: 10 } : p\n        ));\n        \n        const result = await uploadPhoto(photo);\n        \n        // Update the photo with result\n        setPhotos(prev => prev.map(p => \n          p.id === photo.id ? result : p\n        ));\n        \n        return result;\n      });\n\n    await Promise.all(uploadPromises);\n    setUploading(false);\n  };\n\n  const removePhoto = (photoId: string) => {\n    const updatedPhotos = photos.filter(photo => photo.id !== photoId);\n    setPhotos(updatedPhotos);\n    onPhotosChange(updatedPhotos);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Drop Zone */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n          isDragging \n            ? 'border-blue-500 bg-blue-50' \n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n      >\n        <div className=\"space-y-4\">\n          <div className=\"text-6xl\">📸</div>\n          <div>\n            <p className=\"text-lg font-medium\">Drop photos here or click to select</p>\n            <p className=\"text-sm text-gray-500\">Support for JPG, PNG, GIF files</p>\n          </div>\n          <input\n            type=\"file\"\n            multiple\n            accept=\"image/*\"\n            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}\n            className=\"hidden\"\n            id=\"photo-input\"\n          />\n          <label\n            htmlFor=\"photo-input\"\n            className=\"inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 cursor-pointer\"\n          >\n            Select Photos\n          </label>\n        </div>\n      </div>\n\n      {/* Photo Preview Grid */}\n      {photos.length > 0 && (\n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <h3 className=\"text-lg font-medium\">Selected Photos ({photos.length})</h3>\n            <button\n              onClick={uploadAllPhotos}\n              disabled={uploading || photos.every(p => p.uploaded)}\n              className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400\"\n            >\n              {uploading ? 'Uploading...' : 'Upload All'}\n            </button>\n          </div>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n            {photos.map((photo) => (\n              <div key={photo.id} className=\"relative group\">\n                <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n                  <img\n                    src={photo.preview}\n                    alt=\"Preview\"\n                    className=\"w-full h-full object-cover\"\n                  />\n                </div>\n                \n                {/* Upload Progress */}\n                {photo.uploadProgress !== undefined && photo.uploadProgress > 0 && !photo.uploaded && (\n                  <div className=\"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1\">\n                    Uploading... {photo.uploadProgress}%\n                  </div>\n                )}\n                \n                {/* Upload Status */}\n                {photo.uploaded && (\n                  <div className=\"absolute top-2 right-2 bg-green-500 text-white rounded-full p-1\">\n                    ✓\n                  </div>\n                )}\n                \n                {/* Remove Button */}\n                <button\n                  onClick={() => removePhoto(photo.id)}\n                  className=\"absolute top-2 left-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\"\n                >\n                  ×\n                </button>\n                \n                <p className=\"text-xs text-gray-500 mt-1 truncate\">{photo.file.name}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAWe,SAAS,YAAY,EAAE,cAAc,EAAE,OAAO,EAAoB;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAc,EAAE;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,MAAM,mBAAmB,IAAA,oNAAW,EAAC,CAAC;QACpC,MAAM,YAAyB,MAAM,IAAI,CAAC,OACvC,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WACpC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,IAAI,IAAA,iLAAM;gBACV;gBACA,SAAS,IAAI,eAAe,CAAC;gBAC7B,UAAU;gBACV,gBAAgB;YAClB,CAAC;QAEH,MAAM,gBAAgB;eAAI;eAAW;SAAU;QAC/C,UAAU;QACV,eAAe;IACjB,GAAG;QAAC;QAAQ;KAAe;IAE3B,MAAM,aAAa,IAAA,oNAAW,EAAC,CAAC;QAC9B,EAAE,cAAc;QAChB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE;YACxB,iBAAiB,EAAE,YAAY,CAAC,KAAK;QACvC;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,iBAAiB,IAAA,oNAAW,EAAC,CAAC;QAClC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,MAAM,IAAI;QAClC,SAAS,MAAM,CAAC,WAAW;QAC3B,SAAS,MAAM,CAAC,YAAY,MAAM,IAAI,CAAC,IAAI;QAE3C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO;oBAAE,GAAG,KAAK;oBAAE,UAAU;oBAAM,aAAa,OAAO,IAAI;oBAAE,gBAAgB;gBAAI;YACnF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB;YAAE;QACvC;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QAEb,MAAM,iBAAiB,OACpB,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,QAAQ,EAC/B,GAAG,CAAC,OAAO;YACV,0CAA0C;YAC1C,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,gBAAgB;oBAAG,IAAI;YAGrD,MAAM,SAAS,MAAM,YAAY;YAEjC,+BAA+B;YAC/B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG,SAAS;YAG/B,OAAO;QACT;QAEF,MAAM,QAAQ,GAAG,CAAC;QAClB,aAAa;IACf;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC1D,UAAU;QACV,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,+BACA,yCACJ;gBACF,QAAQ;gBACR,YAAY;gBACZ,aAAa;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAW;;;;;;sCAC1B,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BACC,MAAK;4BACL,QAAQ;4BACR,QAAO;4BACP,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAClE,WAAU;4BACV,IAAG;;;;;;sCAEL,8OAAC;4BACC,SAAQ;4BACR,WAAU;sCACX;;;;;;;;;;;;;;;;;YAOJ,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsB;oCAAkB,OAAO,MAAM;oCAAC;;;;;;;0CACpE,8OAAC;gCACC,SAAS;gCACT,UAAU,aAAa,OAAO,KAAK,CAAC,CAAA,IAAK,EAAE,QAAQ;gCACnD,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;kCAIlC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,MAAM,OAAO;4CAClB,KAAI;4CACJ,WAAU;;;;;;;;;;;oCAKb,MAAM,cAAc,KAAK,aAAa,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,QAAQ,kBAChF,8OAAC;wCAAI,WAAU;;4CAAiF;4CAChF,MAAM,cAAc;4CAAC;;;;;;;oCAKtC,MAAM,QAAQ,kBACb,8OAAC;wCAAI,WAAU;kDAAkE;;;;;;kDAMnF,8OAAC;wCACC,SAAS,IAAM,YAAY,MAAM,EAAE;wCACnC,WAAU;kDACX;;;;;;kDAID,8OAAC;wCAAE,WAAU;kDAAuC,MAAM,IAAI,CAAC,IAAI;;;;;;;+BA/B3D,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;AAuChC", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/components/OrderForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { PhotoFile, Order } from '@/types';\nimport { v4 as uuidv4 } from 'uuid';\nimport PhotoUpload from './PhotoUpload';\n\ninterface OrderFormProps {\n  onOrderCreated: (order: Order) => void;\n}\n\nexport default function OrderForm({ onOrderCreated }: OrderFormProps) {\n  const [orderId] = useState(() => uuidv4());\n  const [customerName, setCustomerName] = useState('');\n  const [customerEmail, setCustomerEmail] = useState('');\n  const [notes, setNotes] = useState('');\n  const [photos, setPhotos] = useState<PhotoFile[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!customerName || !customerEmail || photos.length === 0) {\n      alert('Please fill in all required fields and add at least one photo.');\n      return;\n    }\n\n    // Check if all photos are uploaded\n    const unuploadedPhotos = photos.filter(photo => !photo.uploaded);\n    if (unuploadedPhotos.length > 0) {\n      alert('Please wait for all photos to finish uploading before submitting the order.');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const orderData = {\n        id: orderId,\n        customerName,\n        customerEmail,\n        photos,\n        notes\n      };\n\n      const response = await fetch('/api/orders', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(orderData)\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        onOrderCreated(result.order);\n        // Reset form\n        setCustomerName('');\n        setCustomerEmail('');\n        setNotes('');\n        setPhotos([]);\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      console.error('Order submission failed:', error);\n      alert('Failed to create order. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">Create Photo Order</h1>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* Customer Information */}\n          <div className=\"space-y-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800\">Customer Information</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label htmlFor=\"customerName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Full Name *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"customerName\"\n                  value={customerName}\n                  onChange={(e) => setCustomerName(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Enter your full name\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"customerEmail\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address *\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"customerEmail\"\n                  value={customerEmail}\n                  onChange={(e) => setCustomerEmail(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Enter your email address\"\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Special Instructions (Optional)\n              </label>\n              <textarea\n                id=\"notes\"\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Any special instructions for your order...\"\n              />\n            </div>\n          </div>\n\n          {/* Photo Upload Section */}\n          <div className=\"space-y-6\">\n            <h2 className=\"text-xl font-semibold text-gray-800\">Upload Photos</h2>\n            <PhotoUpload onPhotosChange={setPhotos} orderId={orderId} />\n          </div>\n\n          {/* Order Summary */}\n          {photos.length > 0 && (\n            <div className=\"bg-gray-50 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Order Summary</h3>\n              <div className=\"space-y-2\">\n                <p><span className=\"font-medium\">Order ID:</span> {orderId}</p>\n                <p><span className=\"font-medium\">Total Photos:</span> {photos.length}</p>\n                <p><span className=\"font-medium\">Uploaded:</span> {photos.filter(p => p.uploaded).length} / {photos.length}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              disabled={isSubmitting || photos.length === 0 || photos.some(p => !p.uploaded)}\n              className=\"px-8 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? 'Creating Order...' : 'Create Order'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AALA;;;;;AAWe,SAAS,UAAU,EAAE,cAAc,EAAkB;IAClE,MAAM,CAAC,QAAQ,GAAG,IAAA,iNAAQ,EAAC,IAAM,IAAA,iLAAM;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAc,EAAE;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,OAAO,MAAM,KAAK,GAAG;YAC1D,MAAM;YACN;QACF;QAEA,mCAAmC;QACnC,MAAM,mBAAmB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,QAAQ;QAC/D,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,YAAY;gBAChB,IAAI;gBACJ;gBACA;gBACA;gBACA;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,eAAe,OAAO,KAAK;gBAC3B,aAAa;gBACb,gBAAgB;gBAChB,iBAAiB;gBACjB,SAAS;gBACT,UAAU,EAAE;YACd,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAA+C;;;;;;8DAGvF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAA+C;;;;;;8DAGxF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC,4IAAW;oCAAC,gBAAgB;oCAAW,SAAS;;;;;;;;;;;;wBAIlD,OAAO,MAAM,GAAG,mBACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAE,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE;;;;;;;sDACnD,8OAAC;;8DAAE,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAoB;gDAAE,OAAO,MAAM;;;;;;;sDACpE,8OAAC;;8DAAE,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;gDAAC;gDAAI,OAAO,MAAM;;;;;;;;;;;;;;;;;;;sCAMhH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU,gBAAgB,OAAO,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ;gCAC7E,WAAU;0CAET,eAAe,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/components/OrderSuccess.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Order } from '@/types';\n\ninterface OrderSuccessProps {\n  order: Order;\n  onCreateAnother: () => void;\n}\n\nexport default function OrderSuccess({ order, onCreateAnother }: OrderSuccessProps) {\n  return (\n    <div className=\"max-w-2xl mx-auto p-6\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8 text-center\">\n        <div className=\"mb-6\">\n          <div className=\"text-6xl mb-4\">✅</div>\n          <h1 className=\"text-3xl font-bold text-green-600 mb-2\">Order Created Successfully!</h1>\n          <p className=\"text-gray-600\">Your photos have been uploaded and your order is being processed.</p>\n        </div>\n\n        <div className=\"bg-gray-50 rounded-lg p-6 mb-6 text-left\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Order Details</h2>\n          <div className=\"space-y-2\">\n            <p><span className=\"font-medium\">Order ID:</span> {order.id}</p>\n            <p><span className=\"font-medium\">Customer:</span> {order.customerName}</p>\n            <p><span className=\"font-medium\">Email:</span> {order.customerEmail}</p>\n            <p><span className=\"font-medium\">Total Photos:</span> {order.totalPhotos}</p>\n            <p><span className=\"font-medium\">Status:</span> \n              <span className=\"ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm\">\n                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\n              </span>\n            </p>\n            <p><span className=\"font-medium\">Created:</span> {order.createdAt.toLocaleString()}</p>\n            {order.notes && (\n              <p><span className=\"font-medium\">Notes:</span> {order.notes}</p>\n            )}\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          <p className=\"text-gray-600\">\n            We'll process your order and get back to you via email. \n            Your photos are safely stored in our system.\n          </p>\n          \n          <div className=\"space-x-4\">\n            <button\n              onClick={onCreateAnother}\n              className=\"px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700\"\n            >\n              Create Another Order\n            </button>\n            \n            <button\n              onClick={() => window.print()}\n              className=\"px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700\"\n            >\n              Print Receipt\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,aAAa,EAAE,KAAK,EAAE,eAAe,EAAqB;IAChF,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAE,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,MAAM,EAAE;;;;;;;8CAC3D,8OAAC;;sDAAE,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;wCAAE,MAAM,YAAY;;;;;;;8CACrE,8OAAC;;sDAAE,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAa;wCAAE,MAAM,aAAa;;;;;;;8CACnE,8OAAC;;sDAAE,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAoB;wCAAE,MAAM,WAAW;;;;;;;8CACxE,8OAAC;;sDAAE,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC/B,8OAAC;4CAAK,WAAU;sDACb,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;8CAG/D,8OAAC;;sDAAE,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAe;wCAAE,MAAM,SAAS,CAAC,cAAc;;;;;;;gCAC/E,MAAM,KAAK,kBACV,8OAAC;;sDAAE,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAa;wCAAE,MAAM,KAAK;;;;;;;;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAK7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAID,8OAAC;oCACC,SAAS,IAAM,OAAO,KAAK;oCAC3B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Order } from '@/types';\nimport OrderForm from '@/components/OrderForm';\nimport OrderSuccess from '@/components/OrderSuccess';\n\nexport default function Home() {\n  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);\n\n  const handleOrderCreated = (order: Order) => {\n    setCurrentOrder(order);\n  };\n\n  const handleCreateAnother = () => {\n    setCurrentOrder(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Photo Order System</h1>\n          <p className=\"text-gray-600\">Upload your photos to Dropbox and create orders</p>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"py-8\">\n        {currentOrder ? (\n          <OrderSuccess\n            order={currentOrder}\n            onCreateAnother={handleCreateAnother}\n          />\n        ) : (\n          <OrderForm onOrderCreated={handleOrderCreated} />\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-500\">\n            <p>&copy; 2024 Photo Order System. Built with Next.js and Dropbox.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAe;IAE/D,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAK,WAAU;0BACb,6BACC,8OAAC,6IAAY;oBACX,OAAO;oBACP,iBAAiB;;;;;yCAGnB,8OAAC,0IAAS;oBAAC,gBAAgB;;;;;;;;;;;0BAK/B,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist-node/native.js"], "sourcesContent": ["import { randomUUID } from 'node:crypto';\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,YAAA,mIAAU;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist-node/rng.js"], "sourcesContent": ["import { randomFillSync } from 'node:crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,YAAY,IAAI,WAAW;AACjC,IAAI,UAAU,UAAU,MAAM;AACf,SAAS;IACpB,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI;QACjC,IAAA,uIAAc,EAAC;QACf,UAAU;IACd;IACA,OAAO,UAAU,KAAK,CAAC,SAAU,WAAW;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist-node/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist-node/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,wJAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist-node/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC3C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAC9B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,IAAA,2JAAQ,EAAC,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/ultramax/photo-order-app/node_modules/uuid/dist-node/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction _v4(options, buf, offset) {\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    return _v4(options, buf, offset);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,SAAS,IAAI,OAAO,EAAE,GAAG,EAAE,MAAM;IAC7B,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,QAAQ,GAAG,QAAQ,IAAA,sJAAG;IACrD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,SAAS,GAAG,wBAAwB,CAAC;QAC3F;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,IAAA,oKAAe,EAAC;AAC3B;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC5B,IAAI,yJAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,yJAAM,CAAC,UAAU;IAC5B;IACA,OAAO,IAAI,SAAS,KAAK;AAC7B;uCACe", "ignoreList": [0], "debugId": null}}]}